'use client';

import { useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { novoAlunoSchema, type NovoAlunoFormValues } from "../actions/schemas/aluno-schema";
import { createAluno } from "../actions/create-aluno";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { AlertCircle, Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import BasicInfoSection from "./form-sections/basic-info/basic-info";
import ComplementaryInfoSection from "./form-sections/complementary-info/complementary-info";
import AddressInfoSection from "./form-sections/address-info/address-info";
import MedicalInfoSection from "./form-sections/medical-info/medical-info";
import EmergencyInfoSection from "./form-sections/emergency-info/emergency-info";
import FormSuccess from "./form-success";

export default function NovoAlunoForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serverError, setServerError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState(false);
  const [studentId, setStudentId] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const router = useRouter();

  const methods = useForm<NovoAlunoFormValues>({
    resolver: zodResolver(novoAlunoSchema),
    defaultValues: {
      full_name: '',
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      avatar_url: '',
      gender: undefined,
      branch_id: "",
      birth_date: "",
      street: "",
      street_number: "",
      complement: "",
      neighborhood: "",
      city: "",
      state: "",
      postal_code: "",
      emergency_contact_name: "",
      emergency_contact_phone: "",
      emergency_contact_relationship: "",
      health_notes: "",
      allergies: "",
      medical_conditions: "",
      medications: "",
    },
    mode: "onChange"
  });

  const onSubmit = async (data: NovoAlunoFormValues) => {
    setIsSubmitting(true);
    setServerError(null);

    try {
      const result = await createAluno(data);

      if (result.success) {
        setFormSuccess(true);
        setStudentId(result.studentId || null);
        setUserId(result.userId || null);
      } else {
        setServerError(result.message || "Ocorreu um erro ao criar o aluno");
        
        if (result.errors) {
          Object.entries(result.errors).forEach(([key, messages]) => {
            if (key !== "_errors" && key in methods.getValues()) {
              methods.setError(key as any, { 
                type: "server", 
                message: messages?.[0] || "Erro de validação"
              });
            }
          });
        }
      }
    } catch (error) {
      console.error("Erro ao processar formulário:", error);
      setServerError("Ocorreu um erro inesperado. Por favor, tente novamente.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewProfile = () => {
    if (userId) {
      router.push(`/perfil/${userId}`);
    }
  };

  const handleAddAnother = () => {
    setFormSuccess(false);
    setStudentId(null);
    methods.reset();
  };

  return (
    <FormProvider {...methods}>
      {formSuccess ? (
        <FormSuccess 
          userId={userId}
          onViewProfile={handleViewProfile} 
          onAddAnother={handleAddAnother}
        />
      ) : (
        <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-8">
          {serverError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Erro</AlertTitle>
              <AlertDescription>{serverError}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-6">
            <BasicInfoSection />
            <ComplementaryInfoSection />
            <AddressInfoSection />
            <MedicalInfoSection />
            <EmergencyInfoSection />
          </div>

          <div className="flex justify-end gap-4 pt-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => router.push('/alunos')}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Criando...
                </>
              ) : (
                "Criar Aluno"
              )}
            </Button>
          </div>
        </form>
      )}
    </FormProvider>
  );
} 